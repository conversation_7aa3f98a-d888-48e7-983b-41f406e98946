<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dima - Full-Stack Developer</title>
    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- TailwindCSS Configuration -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49',
                        },
                        secondary: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                            950: '#042f2e',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                    animation: {
                        'blob': 'blob 7s infinite',
                        'fade-in': 'fadeIn 1s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                    },
                    keyframes: {
                        blob: {
                            '0%': {
                                borderRadius: '60% 40% 30% 70% / 60% 30% 70% 40%',
                            },
                            '50%': {
                                borderRadius: '30% 60% 70% 40% / 50% 60% 30% 60%',
                            },
                            '100%': {
                                borderRadius: '60% 40% 30% 70% / 60% 30% 70% 40%',
                            },
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                    },
                },
            },
            plugins: [],
        }
    </script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @layer utilities {
            .animation-delay-2000 {
                animation-delay: 2s;
            }
            .animation-delay-4000 {
                animation-delay: 4s;
            }
        }
    </style>
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    <!-- Dark Mode Toggle -->
    <div class="fixed top-5 right-5 z-50">
        <button id="theme-toggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-300" aria-label="Toggle dark mode" tabindex="0">
            <svg id="sun-icon" class="w-6 h-6 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
            <svg id="moon-icon" class="w-6 h-6 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            </svg>
        </button>
    </div>

    <!-- Header Section -->
    <header class="fixed top-0 left-0 w-full bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm z-40 shadow-sm transition-all duration-300">
        <div class="container mx-auto px-4 py-4 md:py-3">
            <nav class="flex justify-between items-center">
                <a href="#home" class="text-2xl font-bold text-primary-600 dark:text-primary-400 transition-colors duration-300" tabindex="0">Dima</a>

                <ul class="hidden md:flex space-x-8">
                    <li><a href="#home" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-300" tabindex="0">Home</a></li>
                    <li><a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-300" tabindex="0">About</a></li>
                    <li><a href="#skills" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-300" tabindex="0">Skills</a></li>
                    <li><a href="#projects" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-300" tabindex="0">Projects</a></li>
                    <li><a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium transition-colors duration-300" tabindex="0">Contact</a></li>
                </ul>

                <button id="mobile-menu-button" class="md:hidden flex items-center p-2 rounded-md text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-300" aria-label="Open menu" tabindex="0">
                    <svg id="menu-icon" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg id="close-icon" class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </nav>
        </div>
    </header>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="fixed inset-0 z-30 bg-white dark:bg-gray-900 transform translate-x-full transition-transform duration-300 ease-in-out md:hidden">
        <div class="flex flex-col h-full justify-center items-center space-y-8 p-4">
            <a href="#home" class="text-xl font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0">Home</a>
            <a href="#about" class="text-xl font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0">About</a>
            <a href="#skills" class="text-xl font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0">Skills</a>
            <a href="#projects" class="text-xl font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0">Projects</a>
            <a href="#contact" class="text-xl font-medium text-gray-900 dark:text-gray-100 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0">Contact</a>
        </div>
    </div>

    <!-- Hero Section -->
    <section id="home" class="relative min-h-screen pt-32 pb-20 overflow-hidden">
        <!-- Background Blobs -->
        <div class="absolute top-0 -left-4 w-72 h-72 bg-primary-300 dark:bg-primary-900 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div class="absolute top-0 -right-4 w-72 h-72 bg-secondary-300 dark:bg-secondary-900 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 dark:bg-pink-900 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="flex flex-col lg:flex-row items-center justify-between gap-12">
                <div class="w-full lg:w-1/2 animate-fade-in">
                    <h2 class="text-lg font-medium text-primary-600 dark:text-primary-400 mb-3">Hi there 👋</h2>
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                        I'm <span class="text-primary-600 dark:text-primary-400">Dima</span>, a Full-Stack Developer
                    </h1>
                    <p class="text-lg text-gray-700 dark:text-gray-300 mb-8 max-w-2xl">
                        This is the place where I opensource stuff and break things. I'm passionate about building modern, responsive web applications that solve real-world problems.
                    </p>

                    <div class="flex flex-wrap gap-4 mb-10">
                        <a href="#projects" class="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900" tabindex="0" aria-label="View my work">View My Work</a>
                        <a href="#contact" class="px-6 py-3 bg-white dark:bg-gray-800 text-primary-600 dark:text-primary-400 border border-primary-600 dark:border-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700 font-medium rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900" tabindex="0" aria-label="Contact me">Contact Me</a>
                    </div>

                    <div class="flex space-x-5">
                        <a href="https://github.com/DimaJoyti" target="_blank" rel="noopener noreferrer" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="GitHub profile">
                            <i class="fab fa-github text-2xl"></i>
                        </a>
                        <a href="https://www.linkedin.com/" target="_blank" rel="noopener noreferrer" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="LinkedIn profile">
                            <i class="fab fa-linkedin text-2xl"></i>
                        </a>
                        <a href="https://twitter.com/" target="_blank" rel="noopener noreferrer" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="Twitter profile">
                            <i class="fab fa-twitter text-2xl"></i>
                        </a>
                    </div>
                </div>

                <div class="w-full lg:w-1/2 flex justify-center lg:justify-end animate-fade-in">
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full blur-2xl opacity-30 animate-blob"></div>
                        <img src="src/assets/images/profile.png" alt="Dima" class="relative z-10 w-64 h-64 md:w-80 md:h-80 object-cover rounded-full border-4 border-white dark:border-gray-800 shadow-xl">
                    </div>
                </div>
            </div>

            <!-- Scroll Indicator -->
            <div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center animate-bounce">
                <span class="text-sm text-gray-600 dark:text-gray-400 mb-2">Scroll Down</span>
                <svg class="w-6 h-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                </svg>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white dark:bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="flex flex-col items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">About Me</h2>
                <div class="w-20 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full mb-8"></div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="animate-slide-up">
                    <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">My Journey</h3>
                    <p class="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                        I'm a developer from Kiev, and my biggest passion is creating modern web applications. I am looking for an exciting new opportunity. I have experience building, delivering, and maintaining scalable and feature-rich enterprise applications.
                    </p>
                    <p class="text-gray-700 dark:text-gray-300 mb-8 leading-relaxed">
                        With a strong foundation in both frontend and backend technologies, I enjoy tackling complex problems and turning ideas into reality through clean, efficient code.
                    </p>

                    <div class="flex flex-wrap gap-4">
                        <a href="#" class="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800" tabindex="0" aria-label="Download CV">Download CV</a>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 animate-slide-up">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 text-center shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
                        <div class="text-primary-600 dark:text-primary-400 text-4xl font-bold mb-2">5+</div>
                        <div class="text-gray-700 dark:text-gray-300 font-medium">Years Experience</div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 text-center shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
                        <div class="text-primary-600 dark:text-primary-400 text-4xl font-bold mb-2">20+</div>
                        <div class="text-gray-700 dark:text-gray-300 font-medium">Projects Completed</div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-8 text-center shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
                        <div class="text-primary-600 dark:text-primary-400 text-4xl font-bold mb-2">10+</div>
                        <div class="text-gray-700 dark:text-gray-300 font-medium">Happy Clients</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-gray-50 dark:bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="flex flex-col items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Skills & Technologies</h2>
                <div class="w-20 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full mb-8"></div>
            </div>

            <!-- Skill Categories -->
            <div class="space-y-16">
                <!-- Frontend Skills -->
                <div class="animate-slide-up">
                    <h3 class="text-xl font-semibold text-primary-600 dark:text-primary-400 mb-6 flex items-center">
                        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Frontend
                    </h3>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/angular/angular-icon.svg" alt="Angular" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Angular</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/reactjs/reactjs-icon.svg" alt="React" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">React</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/vuejs/vuejs-icon.svg" alt="Vue.js" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Vue.js</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/typescriptlang/typescriptlang-icon.svg" alt="TypeScript" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">TypeScript</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/tailwindcss/tailwindcss-icon.svg" alt="Tailwind CSS" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Tailwind</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/sass-lang/sass-lang-icon.svg" alt="Sass" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Sass</p>
                        </div>
                    </div>
                </div>

                <!-- Backend Skills -->
                <div class="animate-slide-up">
                    <h3 class="text-xl font-semibold text-primary-600 dark:text-primary-400 mb-6 flex items-center">
                        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                        </svg>
                        Backend
                    </h3>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/nodejs/nodejs-icon.svg" alt="Node.js" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Node.js</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/python/python-icon.svg" alt="Python" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Python</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/golang/golang-icon.svg" alt="Go" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Go</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/expressjs/expressjs-icon.svg" alt="Express" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Express</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/nestjs/nestjs-icon.svg" alt="NestJS" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">NestJS</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/graphql/graphql-icon.svg" alt="GraphQL" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">GraphQL</p>
                        </div>
                    </div>
                </div>

                <!-- Other Skills -->
                <div class="animate-slide-up">
                    <h3 class="text-xl font-semibold text-primary-600 dark:text-primary-400 mb-6 flex items-center">
                        <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
                        </svg>
                        Mobile & Other
                    </h3>
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/flutterio/flutterio-icon.svg" alt="Flutter" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Flutter</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/docker/docker-icon.svg" alt="Docker" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Docker</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/git-scm/git-scm-icon.svg" alt="Git" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Git</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/mongodb/mongodb-icon.svg" alt="MongoDB" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">MongoDB</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/firebase/firebase-icon.svg" alt="Firebase" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">Firebase</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 flex flex-col items-center shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <img src="https://www.vectorlogo.zone/logos/amazon_aws/amazon_aws-icon.svg" alt="AWS" class="w-12 h-12 mb-4">
                            <p class="font-medium text-gray-900 dark:text-white">AWS</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-white dark:bg-gray-800">
        <div class="container mx-auto px-4">
            <div class="flex flex-col items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">My Projects</h2>
                <div class="w-20 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full mb-8"></div>
            </div>

            <!-- Project Filter -->
            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <button class="filter-btn active px-5 py-2 rounded-full bg-primary-600 text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-300" data-filter="all" tabindex="0">All</button>
                <button class="filter-btn px-5 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-300" data-filter="web" tabindex="0">Web</button>
                <button class="filter-btn px-5 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-300" data-filter="mobile" tabindex="0">Mobile</button>
                <button class="filter-btn px-5 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-300" data-filter="other" tabindex="0">Other</button>
            </div>

            <!-- Projects Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project 1 -->
                <div class="project-item web animate-slide-up bg-gray-50 dark:bg-gray-900 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="relative overflow-hidden group h-56">
                        <img src="https://images.unsplash.com/photo-1557821552-17105176677c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="E-Commerce Platform" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-4 w-full">
                                <div class="flex justify-between items-center">
                                    <a href="#" target="_blank" rel="noopener noreferrer" class="text-white hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="View live demo">
                                        <i class="fas fa-external-link-alt mr-2"></i>Live Demo
                                    </a>
                                    <a href="#" target="_blank" rel="noopener noreferrer" class="text-white hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="View source code">
                                        <i class="fab fa-github mr-2"></i>Source Code
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">E-Commerce Platform</h3>
                        <p class="text-gray-700 dark:text-gray-300 mb-4">A full-stack e-commerce solution with payment integration, user authentication, and admin dashboard.</p>
                        <div class="flex flex-wrap gap-2 mb-2">
                            <span class="px-3 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">React</span>
                            <span class="px-3 py-1 text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">Node.js</span>
                            <span class="px-3 py-1 text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full">MongoDB</span>
                        </div>
                    </div>
                </div>

                <!-- Project 2 -->
                <div class="project-item web animate-slide-up bg-gray-50 dark:bg-gray-900 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="relative overflow-hidden group h-56">
                        <img src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Task Management App" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-4 w-full">
                                <div class="flex justify-between items-center">
                                    <a href="#" target="_blank" rel="noopener noreferrer" class="text-white hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="View live demo">
                                        <i class="fas fa-external-link-alt mr-2"></i>Live Demo
                                    </a>
                                    <a href="#" target="_blank" rel="noopener noreferrer" class="text-white hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="View source code">
                                        <i class="fab fa-github mr-2"></i>Source Code
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Task Management App</h3>
                        <p class="text-gray-700 dark:text-gray-300 mb-4">A collaborative task management application with real-time updates and team collaboration features.</p>
                        <div class="flex flex-wrap gap-2 mb-2">
                            <span class="px-3 py-1 text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-full">Angular</span>
                            <span class="px-3 py-1 text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full">Express</span>
                            <span class="px-3 py-1 text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full">Socket.io</span>
                        </div>
                    </div>
                </div>

                <!-- Project 3 -->
                <div class="project-item mobile animate-slide-up bg-gray-50 dark:bg-gray-900 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="relative overflow-hidden group h-56">
                        <img src="https://images.unsplash.com/photo-1434494878577-86c23bcb06b9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Fitness Tracker" class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                            <div class="p-4 w-full">
                                <div class="flex justify-between items-center">
                                    <a href="#" target="_blank" rel="noopener noreferrer" class="text-white hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="View live demo">
                                        <i class="fas fa-external-link-alt mr-2"></i>Live Demo
                                    </a>
                                    <a href="#" target="_blank" rel="noopener noreferrer" class="text-white hover:text-primary-400 transition-colors duration-300" tabindex="0" aria-label="View source code">
                                        <i class="fab fa-github mr-2"></i>Source Code
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Fitness Tracker</h3>
                        <p class="text-gray-700 dark:text-gray-300 mb-4">A mobile application for tracking workouts, nutrition, and progress with data visualization.</p>
                        <div class="flex flex-wrap gap-2 mb-2">
                            <span class="px-3 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">Flutter</span>
                            <span class="px-3 py-1 text-xs font-medium bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded-full">Firebase</span>
                            <span class="px-3 py-1 text-xs font-medium bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 rounded-full">Chart.js</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- See More Button -->
            <div class="flex justify-center mt-12">
                <a href="https://github.com/DimaJoyti" target="_blank" rel="noopener noreferrer" class="px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800" tabindex="0" aria-label="See more projects on GitHub">
                    <i class="fab fa-github mr-2"></i> See More on GitHub
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50 dark:bg-gray-900">
        <div class="container mx-auto px-4">
            <div class="flex flex-col items-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Get In Touch</h2>
                <div class="w-20 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full mb-8"></div>
                <p class="text-center text-gray-700 dark:text-gray-300 max-w-2xl mb-8">
                    Have a project in mind or want to discuss potential opportunities? Feel free to reach out to me using the form below or through my contact information.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Information -->
                <div class="animate-slide-up">
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                        <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Contact Information</h3>

                        <div class="space-y-6">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-envelope text-primary-600 dark:text-primary-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-1">Email</h4>
                                    <a href="mailto:<EMAIL>" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0"><EMAIL></a>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-map-marker-alt text-primary-600 dark:text-primary-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-1">Location</h4>
                                    <p class="text-gray-700 dark:text-gray-300">Kiev, Ukraine</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 p-3 rounded-full mr-4">
                                    <i class="fas fa-phone text-primary-600 dark:text-primary-400"></i>
                                </div>
                                <div>
                                    <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-1">Phone</h4>
                                    <a href="tel:+380XXXXXXXX" class="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-300" tabindex="0">+380 XX XXX XXXX</a>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Connect With Me</h4>
                            <div class="flex space-x-4">
                                <a href="https://github.com/DimaJoyti" target="_blank" rel="noopener noreferrer" class="bg-gray-200 dark:bg-gray-700 hover:bg-primary-100 dark:hover:bg-primary-900 text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-full transition-colors duration-300" tabindex="0" aria-label="GitHub profile">
                                    <i class="fab fa-github"></i>
                                </a>
                                <a href="https://www.linkedin.com/" target="_blank" rel="noopener noreferrer" class="bg-gray-200 dark:bg-gray-700 hover:bg-primary-100 dark:hover:bg-primary-900 text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-full transition-colors duration-300" tabindex="0" aria-label="LinkedIn profile">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <a href="https://twitter.com/" target="_blank" rel="noopener noreferrer" class="bg-gray-200 dark:bg-gray-700 hover:bg-primary-100 dark:hover:bg-primary-900 text-gray-800 dark:text-gray-200 hover:text-primary-600 dark:hover:text-primary-400 p-3 rounded-full transition-colors duration-300" tabindex="0" aria-label="Twitter profile">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="animate-slide-up">
                    <form id="contactForm" class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
                        <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">Send Me a Message</h3>

                        <div class="space-y-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Name</label>
                                <input type="text" id="name" name="name" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 transition-colors duration-300" required tabindex="0">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Email</label>
                                <input type="email" id="email" name="email" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 transition-colors duration-300" required tabindex="0">
                            </div>

                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subject</label>
                                <input type="text" id="subject" name="subject" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 transition-colors duration-300" required tabindex="0">
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Message</label>
                                <textarea id="message" name="message" rows="5" class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 transition-colors duration-300 resize-none" required tabindex="0"></textarea>
                            </div>
                        </div>

                        <div class="mt-6">
                            <button type="submit" class="w-full px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center justify-center" tabindex="0">
                                <i class="fas fa-paper-plane mr-2"></i> Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-10">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <a href="#home" class="text-2xl font-bold text-primary-400" tabindex="0">Dima</a>
                    <p class="mt-2 text-gray-400">&copy; 2025 Dima. All Rights Reserved.</p>
                </div>

                <div class="flex flex-col space-y-4 md:space-y-0 md:flex-row md:space-x-8">
                    <a href="#home" class="text-gray-300 hover:text-primary-400 transition-colors duration-300" tabindex="0">Home</a>
                    <a href="#about" class="text-gray-300 hover:text-primary-400 transition-colors duration-300" tabindex="0">About</a>
                    <a href="#skills" class="text-gray-300 hover:text-primary-400 transition-colors duration-300" tabindex="0">Skills</a>
                    <a href="#projects" class="text-gray-300 hover:text-primary-400 transition-colors duration-300" tabindex="0">Projects</a>
                    <a href="#contact" class="text-gray-300 hover:text-primary-400 transition-colors duration-300" tabindex="0">Contact</a>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-400">
                <p>Designed and built with <i class="fas fa-heart text-red-500"></i> using TailwindCSS</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#home" id="back-to-top" class="fixed bottom-6 right-6 bg-primary-600 text-white p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" tabindex="0" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- JavaScript -->
    <script>
        // DOM Elements
        const themeToggle = document.getElementById('theme-toggle');
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = document.getElementById('menu-icon');
        const closeIcon = document.getElementById('close-icon');
        const backToTop = document.getElementById('back-to-top');
        const filterBtns = document.querySelectorAll('.filter-btn');
        const projectItems = document.querySelectorAll('.project-item');
        const contactForm = document.getElementById('contactForm');

        // Check for saved theme preference or use system preference
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }

        // Theme toggle functionality
        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            if (document.documentElement.classList.contains('dark')) {
                localStorage.theme = 'dark';
            } else {
                localStorage.theme = 'light';
            }
        });

        // Mobile menu functionality
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('translate-x-full');
            menuIcon.classList.toggle('hidden');
            closeIcon.classList.toggle('hidden');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('#mobile-menu a').forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('translate-x-full');
                menuIcon.classList.remove('hidden');
                closeIcon.classList.add('hidden');
            });
        });

        // Back to top button visibility
        window.addEventListener('scroll', () => {
            if (window.scrollY > 500) {
                backToTop.classList.remove('opacity-0', 'invisible');
                backToTop.classList.add('opacity-100', 'visible');
            } else {
                backToTop.classList.add('opacity-0', 'invisible');
                backToTop.classList.remove('opacity-100', 'visible');
            }
        });

        // Project filtering
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                // Remove active class from all buttons
                filterBtns.forEach(b => {
                    b.classList.remove('active', 'bg-primary-600', 'text-white');
                    b.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-gray-200');
                });

                // Add active class to clicked button
                btn.classList.add('active', 'bg-primary-600', 'text-white');
                btn.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-gray-200');

                const filter = btn.getAttribute('data-filter');

                projectItems.forEach(item => {
                    if (filter === 'all' || item.classList.contains(filter)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // Form submission
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => {
                e.preventDefault();

                // Get form data
                const formData = {
                    name: document.getElementById('name').value,
                    email: document.getElementById('email').value,
                    subject: document.getElementById('subject').value,
                    message: document.getElementById('message').value
                };

                // Show success message (in a real app, you'd send this data to a server)
                alert('Thank you for your message! I will get back to you soon.');
                contactForm.reset();
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80, // Adjust for header height
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Handle keyboard navigation for interactive elements
        document.querySelectorAll('[tabindex="0"]').forEach(element => {
            element.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    element.click();
                }
            });
        });
    </script>
</body>
</html>
